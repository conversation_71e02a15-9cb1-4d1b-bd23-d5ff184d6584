{% extends "admin/base.html" %}

{% block title %}订单详情 - {{ order.order_no }} - 管理后台{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">订单详情</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ url_for('admin.orders') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-1"></i>返回列表
            </a>
        </div>
    </div>
</div>

<!-- 订单基本信息 -->
<div class="row mb-4">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">订单信息</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td class="fw-bold">订单号:</td>
                                <td><span class="font-monospace">{{ order.order_no }}</span></td>
                            </tr>
                            <tr>
                                <td class="fw-bold">订单状态:</td>
                                <td>
                                    <span class="badge bg-{% if order.status == 'pending' %}warning{% elif order.status == 'paid' %}info{% elif order.status == 'processing' %}primary{% elif order.status == 'shipped' %}secondary{% elif order.status == 'delivered' %}success{% else %}danger{% endif %}">
                                        {{ order.get_status_display() }}
                                    </span>
                                </td>
                            </tr>
                            <tr>
                                <td class="fw-bold">支付状态:</td>
                                <td>
                                    <span class="badge bg-{% if order.payment_status == 'paid' %}success{% elif order.payment_status == 'pending' %}warning{% else %}danger{% endif %}">
                                        {{ order.get_payment_status_display() }}
                                    </span>
                                </td>
                            </tr>
                            <tr>
                                <td class="fw-bold">下单时间:</td>
                                <td>{{ format_china_time(order.created_at) }}</td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td class="fw-bold">商品总额:</td>
                                <td>¥{{ "%.2f"|format(order.total_amount) }}</td>
                            </tr>
                            <tr>
                                <td class="fw-bold">优惠金额:</td>
                                <td class="text-success">-¥{{ "%.2f"|format(order.discount_amount) }}</td>
                            </tr>
                            <tr>
                                <td class="fw-bold">实付金额:</td>
                                <td class="fw-bold text-primary">¥{{ "%.2f"|format(order.final_amount) }}</td>
                            </tr>
                            <tr>
                                <td class="fw-bold">支付方式:</td>
                                <td>{{ order.payment_method or '未选择' }}</td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">用户信息</h5>
            </div>
            <div class="card-body">
                <div class="d-flex align-items-center mb-3">
                    <div class="rounded-circle bg-primary d-flex align-items-center justify-content-center me-3" 
                         style="width: 50px; height: 50px; font-size: 1.2rem; color: white;">
                        {{ (order.user.real_name or order.user.username)[0].upper() }}
                    </div>
                    <div>
                        <h6 class="mb-0">{{ order.user.real_name or order.user.username }}</h6>
                        <small class="text-muted">用户名: {{ order.user.username }}</small>
                    </div>
                </div>
                <table class="table table-borderless table-sm">
                    <tr>
                        <td class="fw-bold">邮箱:</td>
                        <td>{{ order.user.email or '未填写' }}</td>
                    </tr>
                    <tr>
                        <td class="fw-bold">手机:</td>
                        <td>{{ order.user.phone or '未填写' }}</td>
                    </tr>
                    <tr>
                        <td class="fw-bold">注册时间:</td>
                        <td>{{ format_china_time(order.user.created_at, '%Y-%m-%d') }}</td>
                    </tr>
                </table>
                <a href="{{ url_for('admin.user_detail', id=order.user.id) }}" class="btn btn-sm btn-outline-primary">
                    <i class="fas fa-user me-1"></i>查看用户详情
                </a>
            </div>
        </div>
    </div>
</div>

<!-- 收货信息 -->
{% if order.shipping_address %}
<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0">收货信息</h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-4">
                <strong>收货人:</strong> {{ order.shipping_name }}
            </div>
            <div class="col-md-4">
                <strong>联系电话:</strong> {{ order.shipping_phone }}
            </div>
            <div class="col-md-4">
                <strong>收货地址:</strong> {{ order.get_address_parts().full_address }}
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- 订单商品 -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0">订单商品</h5>
    </div>
    <div class="card-body p-0">
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead class="table-light">
                    <tr>
                        <th>商品信息</th>
                        <th>规格</th>
                        <th>单价</th>
                        <th>数量</th>
                        <th>小计</th>
                    </tr>
                </thead>
                <tbody>
                    {% for item in order.order_items %}
                    <tr>
                        <td>
                            <div class="d-flex align-items-center">
                                {% if item.product %}
                                <img src="{{ item.product.get_main_image() }}" class="me-3 rounded" 
                                     style="width: 60px; height: 60px; object-fit: cover;" alt="{{ item.product_name }}">
                                {% else %}
                                <div class="me-3 bg-light rounded d-flex align-items-center justify-content-center" 
                                     style="width: 60px; height: 60px;">
                                    <i class="fas fa-image text-muted"></i>
                                </div>
                                {% endif %}
                                <div>
                                    <h6 class="mb-1">{{ item.product_name }}</h6>
                                    {% if item.product %}
                                    <small class="text-muted">商品编号: {{ item.product.id }}</small>
                                    {% else %}
                                    <small class="text-danger">商品已删除</small>
                                    {% endif %}
                                </div>
                            </div>
                        </td>
                        <td>
                            {% if item.get_attributes_display() %}
                            <small class="text-muted">{{ item.get_attributes_display() }}</small>
                            {% else %}
                            <span class="text-muted">无规格</span>
                            {% endif %}
                        </td>
                        <td>¥{{ "%.2f"|format(item.unit_price) }}</td>
                        <td>{{ item.quantity }}</td>
                        <td>¥{{ "%.2f"|format(item.total_price) }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- 备注信息 -->
{% if order.notes %}
<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0">订单备注</h5>
    </div>
    <div class="card-body">
        <p class="mb-0">{{ order.notes }}</p>
    </div>
</div>
{% endif %}

<!-- 操作区域 -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">订单操作</h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <h6>更新订单状态</h6>
                <div class="d-flex gap-2">
                    <select class="form-select" id="orderStatus" style="width: auto;">
                        <option value="pending" {% if order.status == 'pending' %}selected{% endif %}>待付款</option>
                        <option value="paid" {% if order.status == 'paid' %}selected{% endif %}>已付款</option>
                        <option value="processing" {% if order.status == 'processing' %}selected{% endif %}>处理中</option>
                        <option value="shipped" {% if order.status == 'shipped' %}selected{% endif %}>已发货</option>
                        <option value="delivered" {% if order.status == 'delivered' %}selected{% endif %}>已完成</option>
                        <option value="cancelled" {% if order.status == 'cancelled' %}selected{% endif %}>已取消</option>
                    </select>
                    <button type="button" class="btn btn-primary" onclick="updateOrderStatus()">
                        <i class="fas fa-save me-1"></i>更新状态
                    </button>
                </div>
            </div>
            <div class="col-md-6">
                <h6>其他操作</h6>
                <div class="d-flex gap-2">
                    <!-- 打印下拉菜单 -->
                    <div class="btn-group">
                        <button type="button" class="btn btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-print me-1"></i>打印选项
                    </button>
                        <ul class="dropdown-menu">
                            <li>
                                <a class="dropdown-item" href="javascript:void(0)" onclick="printOrder()">
                                    <i class="fas fa-file-alt me-2"></i>打印订单详情
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="javascript:void(0)" onclick="printProduction()">
                                    <i class="fas fa-cogs me-2 text-danger"></i>打印生产单
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="javascript:void(0)" onclick="printShipping()">
                                    <i class="fas fa-truck me-2 text-success"></i>打印发货单
                                </a>
                            </li>
                        </ul>
                    </div>
                    <button type="button" class="btn btn-outline-info" onclick="exportOrder()">
                        <i class="fas fa-download me-1"></i>导出订单
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function updateOrderStatus() {
    const newStatus = document.getElementById('orderStatus').value;
    const orderId = {{ order.id }};
    
    if (!confirm('确定要更新订单状态吗？')) {
        return;
    }
    
    fetch(`/admin/orders/${orderId}/update-status`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            status: newStatus
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', data.message);
            // 刷新页面以显示更新后的状态
            setTimeout(() => {
                location.reload();
            }, 1000);
        } else {
            showAlert('danger', data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('danger', '状态更新失败，请重试');
    });
}

function printOrder() {
    // 打开专门的打印页面
    var printUrl = '/admin/orders/{{ order.id }}/print';
    window.open(printUrl, '_blank');
}

function printProduction() {
    // 打印生产单
    var printUrl = '/admin/orders/{{ order.id }}/print-production';
    window.open(printUrl, '_blank');
}

function printShipping() {
    // 打印发货单
    var printUrl = '/admin/orders/{{ order.id }}/print-shipping';
    window.open(printUrl, '_blank');
}

function exportOrder() {
    // 实现导出功能
    showAlert('info', '导出功能正在开发中...');
}

function showAlert(type, message) {
    const alertHtml = `
        <div class="alert alert-${type} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    document.querySelector('.container-fluid').insertAdjacentHTML('afterbegin', alertHtml);
    
    // 3秒后自动关闭
    setTimeout(function() {
        const alert = document.querySelector('.alert');
        if (alert) {
            alert.remove();
        }
    }, 3000);
}
</script>
{% endblock %} 